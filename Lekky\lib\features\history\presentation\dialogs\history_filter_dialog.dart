import 'package:flutter/material.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../../core/widgets/date_range_selector_widget.dart';
import '../../../../core/di/service_locator.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';

/// Dialog for filtering history entries
class HistoryFilterDialog extends StatefulWidget {
  /// Current filter type
  final EntryFilterType filterType;

  /// Current sort order
  final EntrySortOrder sortOrder;

  /// Current start date
  final DateTime? startDate;

  /// Current end date
  final DateTime? endDate;

  /// Constructor
  const HistoryFilterDialog({
    Key? key,
    required this.filterType,
    required this.sortOrder,
    this.startDate,
    this.endDate,
  }) : super(key: key);

  @override
  State<HistoryFilterDialog> createState() => _HistoryFilterDialogState();
}

class _HistoryFilterDialogState extends State<HistoryFilterDialog> {
  late EntryFilterType _filterType;
  late EntrySortOrder _sortOrder;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _datesInitialized = false;

  @override
  void initState() {
    super.initState();
    _filterType = widget.filterType;
    _sortOrder = widget.sortOrder;
    _startDate = widget.startDate;
    _endDate = widget.endDate;

    // Initialize default dates if not provided
    if (_startDate == null || _endDate == null) {
      _initializeDefaultDates();
    } else {
      _datesInitialized = true;
    }
  }

  /// Initialize default dates from meter readings
  Future<void> _initializeDefaultDates() async {
    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final allReadings = await meterReadingRepo.getAllMeterReadings();

      if (allReadings.isNotEmpty && mounted) {
        setState(() {
          // Set from date to first meter reading date if not already set
          _startDate ??= allReadings.last.date;
          // Set to date to latest meter reading date if not already set
          _endDate ??= allReadings.first.date;
          _datesInitialized = true;
        });
      } else if (mounted) {
        setState(() {
          _datesInitialized = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _datesInitialized = true;
        });
      }
    }
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth < 600 ? screenWidth * 0.95 : 500.0;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28,
      ),
      child: Container(
        width: dialogWidth,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDialogHeader(context),
            const SizedBox(height: 24),
            _buildFilterTypeDropdown(context),
            const SizedBox(height: 16),
            _buildSortOrderDropdown(context),
            const SizedBox(height: 16),
            _buildDateRangeSelector(context),
            const SizedBox(height: 32),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.tune,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Filter Entries',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build filter type dropdown
  Widget _buildFilterTypeDropdown(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Entry Type',
          style: TextStyle(
            fontSize: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: theme.colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<EntryFilterType>(
              value: _filterType,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              borderRadius: BorderRadius.circular(8),
              items: EntryFilterType.values.map((type) {
                String label;
                switch (type) {
                  case EntryFilterType.all:
                    label = 'All Entries';
                    break;
                  case EntryFilterType.meterReadings:
                    label = 'Meter Readings';
                    break;
                  case EntryFilterType.topUps:
                    label = 'Top-ups';
                    break;
                  case EntryFilterType.invalid:
                    label = 'Invalid Entries';
                    break;
                }

                if (type == EntryFilterType.invalid) {
                  return DropdownMenuItem<EntryFilterType>(
                    value: type,
                    child: Row(
                      children: [
                        const Icon(
                          Icons.warning_amber,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(label),
                      ],
                    ),
                  );
                }

                return DropdownMenuItem<EntryFilterType>(
                  value: type,
                  child: Text(label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _filterType = value;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build sort order dropdown
  Widget _buildSortOrderDropdown(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sort',
          style: TextStyle(
            fontSize: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: theme.colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<EntrySortOrder>(
              value: _sortOrder,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              borderRadius: BorderRadius.circular(8),
              items: EntrySortOrder.values.map((order) {
                String label;
                switch (order) {
                  case EntrySortOrder.newestFirst:
                    label = 'Newest First';
                    break;
                  case EntrySortOrder.oldestFirst:
                    label = 'Oldest First';
                    break;
                }

                return DropdownMenuItem<EntrySortOrder>(
                  value: order,
                  child: Text(label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _sortOrder = value;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build date range selector
  Widget _buildDateRangeSelector(BuildContext context) {
    // Show loading indicator while dates are being initialized
    if (!_datesInitialized) {
      return const SizedBox(
        height: 80,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return DateRangeSelectorWidget(
      fromDate: _startDate,
      toDate: _endDate,
      onFromDateChanged: (date) {
        setState(() {
          _startDate = date;
        });
      },
      onToDateChanged: (date) {
        setState(() {
          _endDate = date;
        });
      },
      onClearDateRange: () async {
        // Reset to default dates
        try {
          final meterReadingRepo = serviceLocator<MeterReadingRepository>();
          final allReadings = await meterReadingRepo.getAllMeterReadings();

          if (allReadings.isNotEmpty && mounted) {
            setState(() {
              _startDate = allReadings.last.date;
              _endDate = allReadings.first.date;
            });
          }
        } catch (e) {
          // If error, just clear the dates
          setState(() {
            _startDate = null;
            _endDate = null;
          });
        }
      },
      getFromDateConstraints: _getFromDateConstraints,
      getToDateConstraints: _getToDateConstraints,
      themeContext: 'history',
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            onPressed: () => Navigator.of(context).pop(),
            type: LekkyButtonType.secondary,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: LekkyButton(
            text: 'Apply',
            onPressed: () {
              Navigator.of(context).pop({
                'filterType': _filterType,
                'sortOrder': _sortOrder,
                'startDate': _startDate,
                'endDate': _endDate,
              });
            },
            type: LekkyButtonType.primary,
          ),
        ),
      ],
    );
  }

  /// Get constraints for from date picker
  Future<DateConstraints> _getFromDateConstraints() async {
    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final allReadings = await meterReadingRepo.getAllMeterReadings();

      if (allReadings.isEmpty) {
        return DateConstraints(
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
          defaultDate: DateTime.now(),
        );
      }

      // Get first meter reading date as default from date
      final firstReadingDate = allReadings.last.date;

      return DateConstraints(
        firstDate: firstReadingDate,
        lastDate: _endDate ?? DateTime.now(),
        defaultDate: firstReadingDate,
      );
    } catch (e) {
      return DateConstraints(
        firstDate: DateTime(2020),
        lastDate: DateTime.now(),
        defaultDate: DateTime.now(),
      );
    }
  }

  /// Get constraints for to date picker
  Future<DateConstraints> _getToDateConstraints() async {
    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final allReadings = await meterReadingRepo.getAllMeterReadings();

      if (allReadings.isEmpty) {
        return DateConstraints(
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
          defaultDate: DateTime.now(),
        );
      }

      // Get latest meter reading date as default to date
      final latestReadingDate = allReadings.first.date;

      return DateConstraints(
        firstDate: _startDate ?? allReadings.last.date,
        lastDate: latestReadingDate,
        defaultDate: latestReadingDate,
      );
    } catch (e) {
      return DateConstraints(
        firstDate: _startDate ?? DateTime(2020),
        lastDate: DateTime.now(),
        defaultDate: DateTime.now(),
      );
    }
  }
}
