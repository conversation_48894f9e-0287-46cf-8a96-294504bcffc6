import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/validation_provider.dart';
import '../controllers/validation_dashboard_controller.dart';
import '../widgets/issue_card.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../domain/models/validation_issue.dart';
import '../../domain/models/integrity_report.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../entries/presentation/dialogs/edit_entry_dialog.dart';
import '../../../../core/providers/date_formatter_provider.dart';

/// Dialog for viewing and managing validation issues
class ValidationDialog extends ConsumerStatefulWidget {
  const ValidationDialog({super.key});

  @override
  ConsumerState<ValidationDialog> createState() => _ValidationDialogState();
}

class _ValidationDialogState extends ConsumerState<ValidationDialog> {
  @override
  void initState() {
    super.initState();
    // Ensure data is refreshed when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(validationControllerProvider.notifier).refresh();
    });
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final calculatedWidth = screenWidth < 600
        ? screenWidth * 0.95 // Small and medium screens
        : 500.0; // Fixed width for larger screens

    debugPrint(
        'ValidationDialog: screenWidth=$screenWidth, calculatedWidth=$calculatedWidth');
    return calculatedWidth;
  }

  @override
  Widget build(BuildContext context) {
    final validationState = ref.watch(validationControllerProvider);
    final validationNotifier = ref.read(validationControllerProvider.notifier);

    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28, // Same as notification dialog for 7% increased height
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // App Banner with validation colors and close button
          Stack(
            children: [
              AppBanner(
                message: 'Entry Validation',
                icon: Icons.check_circle,
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
                onDismiss: () => Navigator.of(context).pop(),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              // Filter icon positioned in banner area
              Positioned(
                top: 12,
                right: 56, // Position left of close button
                child: _buildFilterIcon(context, validationState),
              ),
              // Clear filters icon positioned in banner area
              Positioned(
                top: 12,
                right: 96, // Position left of filter icon
                child: IconButton(
                  icon: const Icon(
                    Icons.filter_list_off,
                    color: Colors.white,
                    size: 24,
                  ),
                  onPressed: validationNotifier.clearFilters,
                  tooltip: 'Clear Filters',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ),
            ],
          ),
          // Main content based on validation state
          Expanded(
            child: _buildValidationContent(context, validationState),
          ),
        ],
      ),
    );
  }

  /// Build the filter icon with dynamic styling and animation
  Widget _buildFilterIcon(BuildContext context, ValidationState state) {
    final filterColor = _getFilterIconColor(state.filterType);
    final isFilterActive = state.filterType != ValidationIssueFilterType.all;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: EdgeInsets.zero,
      decoration: isFilterActive
          ? BoxDecoration(
              color: filterColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            )
          : null,
      child: AnimatedScale(
        scale: isFilterActive ? 1.1 : 1.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: IconButton(
          icon: Icon(
            Icons.tune,
            color: isFilterActive ? filterColor : Colors.white,
            size: 24,
          ),
          onPressed: () => _showFilterDialog(context),
          tooltip: isFilterActive ? 'Filters applied' : 'Filter issues',
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ),
    );
  }

  /// Get filter icon color based on filter type
  Color _getFilterIconColor(ValidationIssueFilterType filterType) {
    switch (filterType) {
      case ValidationIssueFilterType.all:
        return Colors.white;
      case ValidationIssueFilterType.highSeverity:
        return Colors.red;
      case ValidationIssueFilterType.mediumSeverity:
        return Colors.orange;
      case ValidationIssueFilterType.lowSeverity:
        return Colors.blue;
    }
  }

  /// Build validation content
  Widget _buildValidationContent(
    BuildContext context,
    ValidationState state,
  ) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.errorMessage != null) {
      return _buildErrorContent(context, state);
    }

    if (state.filteredIssues.isEmpty) {
      return _buildEmptyState(context, state);
    }

    return Column(
      children: [
        // Summary card
        if (state.integrityReport != null)
          _buildSummaryCard(state.integrityReport!),

        // Issues list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: state.filteredIssues.length,
            itemBuilder: (context, index) {
              final issue = state.filteredIssues[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: IssueCard(
                  issue: issue,
                  onFix: (issue) => _showEditEntry(context, issue),
                  onIgnore: null,
                  onTap: (issue) => _showIssueDetails(context, issue),
                  isSelected: false,
                  selectionMode: false,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Build error content
  Widget _buildErrorContent(
    BuildContext context,
    ValidationState state,
  ) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Validation Issues',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            state.errorMessage!,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),
          LekkyButton(
            text: 'Retry',
            onPressed: () =>
                ref.read(validationControllerProvider.notifier).refresh(),
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
          ),
        ],
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState(
    BuildContext context,
    ValidationState state,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle_outline,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'No validation issues found',
              style: AppTextStyles.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'All your data is valid and consistent',
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            LekkyButton(
              text: 'Refresh',
              type: LekkyButtonType.primary,
              onPressed: () =>
                  ref.read(validationControllerProvider.notifier).refresh(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build summary card
  Widget _buildSummaryCard(IntegrityReport report) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Data Integrity Summary',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const Divider(),
          const SizedBox(height: 8),

          // Entries checked
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Entries Checked:'),
              Text(
                '${report.totalEntriesChecked}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Valid entries
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Valid Entries:'),
              Text(
                '${report.validEntriesCount} (${report.validPercentage.toStringAsFixed(1)}%)',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Invalid entries
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Invalid Entries:'),
              Text(
                '${report.invalidEntriesCount} (${report.invalidPercentage.toStringAsFixed(1)}%)',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Issues by severity
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('High Severity Issues:'),
              Text(
                '${report.highSeverityCount}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Medium Severity Issues:'),
              Text(
                '${report.mediumSeverityCount}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Low Severity Issues:'),
              Text(
                '${report.lowSeverityCount}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Last check time
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Last Check:'),
              Text(
                _formatDateTime(report.generatedAt),
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Show filter dialog
  void _showFilterDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;
    final validationNotifier = ref.read(validationControllerProvider.notifier);
    final currentState = ref.read(validationControllerProvider);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 24,
        insetPadding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: 28,
        ),
        child: Container(
          width: dialogWidth,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.tune,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Filter Issues',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: 'Close',
                  ),
                ],
              ),
              const SizedBox(height: 24),
              DropdownButtonFormField<ValidationIssueFilterType>(
                value: currentState.filterType,
                decoration: const InputDecoration(
                  labelText: 'Filter by Severity',
                  border: OutlineInputBorder(),
                ),
                items: [
                  ValidationIssueFilterType.all,
                  ValidationIssueFilterType.highSeverity,
                  ValidationIssueFilterType.mediumSeverity,
                  ValidationIssueFilterType.lowSeverity,
                ].map((type) {
                  String label;
                  switch (type) {
                    case ValidationIssueFilterType.all:
                      label = 'All Issues';
                      break;
                    case ValidationIssueFilterType.highSeverity:
                      label = 'High Severity';
                      break;
                    case ValidationIssueFilterType.mediumSeverity:
                      label = 'Medium Severity';
                      break;
                    case ValidationIssueFilterType.lowSeverity:
                      label = 'Low Severity';
                      break;
                  }
                  return DropdownMenuItem<ValidationIssueFilterType>(
                    value: type,
                    child: Text(label),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    validationNotifier.setFilterType(value);
                    Navigator.of(context).pop();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show edit entry dialog for an issue
  Future<void> _showEditEntry(
      BuildContext context, ValidationIssue issue) async {
    final validationNotifier = ref.read(validationControllerProvider.notifier);

    // Get the entry associated with the issue
    final entry = await validationNotifier.getEntryForIssue(issue);

    if (entry == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Entry not found')),
        );
      }
      return;
    }

    if (!context.mounted) return;

    // Get currency symbol from preferences
    final settingsAsync = ref.read(settingsProvider);
    final currencySymbol = settingsAsync.when(
      data: (settings) => settings.currencySymbol,
      loading: () => '₦', // Default fallback
      error: (_, __) => '₦', // Default fallback
    );

    // Navigate to edit entry based on entry type
    if (entry is MeterReading) {
      await showDialog(
        context: context,
        builder: (context) => EditEntryDialog(
          meterReading: entry,
          topUp: null,
          currencySymbol: currencySymbol,
          onEntryUpdated: () => validationNotifier.refresh(),
          onEntryDeleted: () => validationNotifier.refresh(),
        ),
      );
    } else if (entry is TopUp) {
      await showDialog(
        context: context,
        builder: (context) => EditEntryDialog(
          meterReading: null,
          topUp: entry,
          currencySymbol: currencySymbol,
          onEntryUpdated: () => validationNotifier.refresh(),
          onEntryDeleted: () => validationNotifier.refresh(),
        ),
      );
    }
  }

  /// Show issue details
  Future<void> _showIssueDetails(
      BuildContext context, ValidationIssue issue) async {
    final validationNotifier = ref.read(validationControllerProvider.notifier);

    // Get the entry associated with the issue
    final entry = await validationNotifier.getEntryForIssue(issue);

    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text(
          'Issue Details',
          style: AppTextStyles.titleMedium,
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Issue type and severity
              Row(
                children: [
                  _buildIssueTypeIcon(issue.type),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getIssueTypeText(issue.type),
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildSeverityBadge(issue.severity),
                ],
              ),
              const Divider(),

              // Issue message
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  _formatIssueMessage(issue),
                  style: AppTextStyles.bodyMedium,
                ),
              ),

              // Entry details if available
              if (entry != null) _buildEntryDetails(entry),

              // Metadata if available
              if (issue.metadata != null && issue.metadata!.isNotEmpty)
                _buildMetadataDetails(issue.metadata!),

              // Detection date
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'Detected: ${_formatDateTime(issue.detectedAt)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          LekkyButton(
            text: 'Close',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(dialogContext).pop(),
          ),
          const SizedBox(width: 8),
          LekkyButton(
            text: 'Edit Entry',
            type: LekkyButtonType.special,
            size: LekkyButtonSize.compact,
            onPressed: () {
              Navigator.of(dialogContext).pop();
              _showEditEntry(context, issue);
            },
          ),
        ],
      ),
    );
  }

  /// Format a date for display
  String _formatDate(DateTime date) {
    return ref.watch(dateFormatterProvider).formatDateForValidation(date);
  }

  /// Format a date and time for display
  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Build the issue type icon
  Widget _buildIssueTypeIcon(ValidationIssueType type) {
    IconData iconData;
    Color iconColor;

    switch (type) {
      case ValidationIssueType.negativeValue:
        iconData = Icons.remove_circle_outline;
        iconColor = Colors.red;
        break;
      case ValidationIssueType.futureDate:
        iconData = Icons.event_busy;
        iconColor = Colors.orange;
        break;
      case ValidationIssueType.chronologicalOrder:
        iconData = Icons.swap_vert;
        iconColor = Colors.red;
        break;
      case ValidationIssueType.balanceInconsistency:
        iconData = Icons.account_balance_wallet;
        iconColor = Colors.red;
        break;
      case ValidationIssueType.duplicateEntry:
        iconData = Icons.content_copy;
        iconColor = Colors.orange;
        break;
      case ValidationIssueType.missingEntry:
        iconData = Icons.calendar_today;
        iconColor = Colors.blue;
        break;
      case ValidationIssueType.other:
        iconData = Icons.error_outline;
        iconColor = Colors.red;
        break;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 24,
    );
  }

  /// Build the severity badge
  Widget _buildSeverityBadge(ValidationIssueSeverity severity) {
    Color badgeColor;
    String severityText;

    switch (severity) {
      case ValidationIssueSeverity.high:
        badgeColor = Colors.red;
        severityText = 'High';
        break;
      case ValidationIssueSeverity.medium:
        badgeColor = Colors.orange;
        severityText = 'Medium';
        break;
      case ValidationIssueSeverity.low:
        badgeColor = Colors.blue;
        severityText = 'Low';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        severityText,
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Get the issue type text
  String _getIssueTypeText(ValidationIssueType type) {
    switch (type) {
      case ValidationIssueType.negativeValue:
        return 'Negative Value';
      case ValidationIssueType.futureDate:
        return 'Future Date';
      case ValidationIssueType.chronologicalOrder:
        return 'Chronological Order';
      case ValidationIssueType.balanceInconsistency:
        return 'Balance Inconsistency';
      case ValidationIssueType.duplicateEntry:
        return 'Duplicate Entry';
      case ValidationIssueType.missingEntry:
        return 'Missing Entry';
      case ValidationIssueType.other:
        return 'Other Issue';
    }
  }

  /// Build entry details
  Widget _buildEntryDetails(dynamic entry) {
    if (entry == null) {
      return const SizedBox.shrink();
    }

    if (entry is MeterReading) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Text(
            'Meter Reading Details',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text('ID: ${entry.id}'),
          Text('Value: ${entry.value}'),
          Text('Date: ${_formatDate(entry.date)}'),
          Text('Valid: ${entry.isValid ? 'Yes' : 'No'}'),
          if (entry.notes != null && entry.notes!.isNotEmpty)
            Text('Notes: ${entry.notes}'),
        ],
      );
    } else if (entry is TopUp) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Text(
            'Top-up Details',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text('ID: ${entry.id}'),
          Text('Amount: ${entry.amount}'),
          Text('Date: ${_formatDate(entry.date)}'),
          if (entry.notes != null && entry.notes!.isNotEmpty)
            Text('Notes: ${entry.notes}'),
          if (entry.paymentMethod != null && entry.paymentMethod!.isNotEmpty)
            Text('Payment Method: ${entry.paymentMethod}'),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  /// Build metadata details
  Widget _buildMetadataDetails(Map<String, dynamic> metadata) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          'Additional Information',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...metadata.entries.map((entry) {
          return Text('${_formatMetadataKey(entry.key)}: ${entry.value}');
        }),
      ],
    );
  }

  /// Format a metadata key for display
  String _formatMetadataKey(String key) {
    // Convert snake_case to Title Case
    return key
        .split('_')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }

  /// Format issue message to show "expected less than" for balance inconsistency
  String _formatIssueMessage(ValidationIssue issue) {
    final settingsAsync = ref.read(settingsProvider);
    final currencySymbol = settingsAsync.when(
      data: (settings) => settings.currencySymbol,
      loading: () => '₦', // Default fallback
      error: (_, __) => '₦', // Default fallback
    );

    if (issue.type == ValidationIssueType.balanceInconsistency &&
        issue.metadata != null &&
        issue.metadata!.containsKey('expected_value')) {
      final expectedValue = issue.metadata!['expected_value'] as double;

      // Include detected date and expected date in the message
      String message = 'Balance inconsistency detected: ';

      if (issue.metadata!.containsKey('detected_date')) {
        final detectedDate = issue.metadata!['detected_date'] as DateTime;
        message +=
            '${ref.watch(dateFormatterProvider).formatDateForValidation(detectedDate)}, ';
      }

      message +=
          'expected less than $currencySymbol${expectedValue.toStringAsFixed(2)}';

      if (issue.metadata!.containsKey('expected_date')) {
        final expectedDate = issue.metadata!['expected_date'] as DateTime;
        message +=
            ' by ${ref.watch(dateFormatterProvider).formatDateForValidation(expectedDate)}';
      }

      return message;
    }
    return issue.message;
  }
}

/// Helper function to show validation dialog
void showValidationDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const ValidationDialog(),
  );
}
