import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/settings_state.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/constants/currency_constants.dart';
import '../../../../features/setup/presentation/widgets/radio_option.dart';

/// Currency settings screen
class CurrencyScreen extends ConsumerWidget {
  /// Constructor
  const CurrencyScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return Column(
                children: [
                  // Banner with back arrow
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: AppBanner(
                      message: '← Currency',
                      gradientColors: AppColors.getSettingsMainCardGradient(
                          Theme.of(context).brightness == Brightness.dark),
                      textColor: AppColors.getAppBarTextColor('settings',
                          Theme.of(context).brightness == Brightness.dark),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Card(
                            margin: const EdgeInsets.all(8.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Currency section header
                                  Row(
                                    children: [
                                      const Icon(Icons.currency_exchange,
                                          color: Colors.blue),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'Currency',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Current: ${settings.currency}',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Theme.of(context)
                                                    .textTheme
                                                    .bodySmall
                                                    ?.color,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'Select the currency for your meter readings.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 8),

                                  // Currency options
                                  _buildCurrencyOptions(settings, ref),

                                  const SizedBox(height: 8),
                                  Text(
                                    'Tip: Select the currency that matches your electricity bills.',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontStyle: FontStyle.italic,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.color,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  Widget _buildCurrencyOptions(SettingsState settings, WidgetRef ref) {
    final currenciesByRegion = RegionalConstants.getCurrenciesByRegion();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: currenciesByRegion.entries.map((regionEntry) {
        final region = regionEntry.key;
        final currencies = regionEntry.value;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Region header
            Padding(
              padding: const EdgeInsets.only(top: 16, bottom: 8),
              child: Text(
                region,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey,
                ),
              ),
            ),

            // Currencies in 2 columns with reduced spacing
            _buildCurrencyGrid(currencies, settings, ref),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildCurrencyGrid(
      List<CurrencyData> currencies, SettingsState settings, WidgetRef ref) {
    // Split currencies into two columns
    final int midPoint = (currencies.length / 2).ceil();
    final firstColumn = currencies.sublist(0, midPoint);
    final secondColumn = currencies.length > midPoint
        ? currencies.sublist(midPoint)
        : <CurrencyData>[];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First column
        Expanded(
          child: Column(
            children: firstColumn.map((currency) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: RadioOption<String>(
                  value: currency.code,
                  groupValue: settings.currency,
                  onChanged: (value) => ref
                      .read(settingsProvider.notifier)
                      .updateCurrency(value, currency.symbol),
                  title: '${currency.symbol} ${currency.code}',
                ),
              );
            }).toList(),
          ),
        ),

        // Second column
        Expanded(
          child: Column(
            children: secondColumn.map((currency) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: RadioOption<String>(
                  value: currency.code,
                  groupValue: settings.currency,
                  onChanged: (value) => ref
                      .read(settingsProvider.notifier)
                      .updateCurrency(value, currency.symbol),
                  title: '${currency.symbol} ${currency.code}',
                  icon: currency.icon,
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
