import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/constants/currency_constants.dart';

/// Region settings screen
class RegionSettingsScreen extends ConsumerStatefulWidget {
  /// Constructor
  const RegionSettingsScreen({super.key});

  @override
  ConsumerState<RegionSettingsScreen> createState() =>
      _RegionSettingsScreenState();
}

class _RegionSettingsScreenState extends ConsumerState<RegionSettingsScreen> {
  late String _selectedLanguage;
  late String _selectedCurrency;
  late String _selectedCurrencySymbol;

  @override
  void initState() {
    super.initState();
    // Initialize with defaults, will be updated when provider loads
    _selectedLanguage = 'English';
    _selectedCurrency = 'GBP';
    _selectedCurrencySymbol = '£';
  }

  @override
  Widget build(BuildContext context) {
    final settingsAsync = ref.watch(settingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Region'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: settingsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error loading settings: $error'),
        ),
        data: (settings) {
          // Update local state when data loads
          if (_selectedLanguage != settings.language ||
              _selectedCurrency != settings.currency ||
              _selectedCurrencySymbol != settings.currencySymbol) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _selectedLanguage = settings.language;
                _selectedCurrency = settings.currency;
                _selectedCurrencySymbol = settings.currencySymbol;
              });
            });
          }
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Language section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.language, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Language',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: $_selectedLanguage',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Select your preferred language',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Language options
                      _buildLanguageOption('English'),
                      _buildLanguageOption('Spanish'),
                      _buildLanguageOption('French'),
                      _buildLanguageOption('German'),
                      _buildLanguageOption('Italian'),
                      _buildLanguageOption('Portuguese'),
                      _buildLanguageOption('Russian'),
                      _buildLanguageOption('Chinese'),
                      _buildLanguageOption('Japanese'),
                    ],
                  ),
                ),
              ),

              // Currency section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.attach_money, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Currency',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: $_selectedCurrencySymbol',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Select your preferred currency',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Currency options in two columns with reduced spacing
                      _buildCurrencyGrid(),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLanguageOption(String language) {
    return RadioListTile<String>(
      title: Text(language),
      value: language,
      groupValue: _selectedLanguage,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedLanguage = value;
          });
          ref.read(settingsProvider.notifier).updateLanguage(value);
        }
      },
    );
  }

  Widget _buildCurrencyGrid() {
    // Use first 12 currencies for this screen to keep it manageable
    final currencies = RegionalConstants.currencies.take(12).toList();

    // Split currencies into two columns
    final int midPoint = (currencies.length / 2).ceil();
    final firstColumn = currencies.sublist(0, midPoint);
    final secondColumn = currencies.length > midPoint
        ? currencies.sublist(midPoint)
        : <CurrencyData>[];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First column
        Expanded(
          child: Column(
            children: firstColumn.map((currency) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 2),
                child: _buildCurrencyOption(currency.code, currency.symbol),
              );
            }).toList(),
          ),
        ),
        // Second column
        Expanded(
          child: Column(
            children: secondColumn.map((currency) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 2),
                child: _buildCurrencyOption(currency.code, currency.symbol),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildCurrencyOption(String currency, String symbol) {
    return RadioListTile<String>(
      title: Text(currency),
      value: currency,
      groupValue: _selectedCurrency,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedCurrency = value;
            _selectedCurrencySymbol = symbol;
          });
          ref.read(settingsProvider.notifier).updateCurrency(value, symbol);
        }
      },
    );
  }
}
