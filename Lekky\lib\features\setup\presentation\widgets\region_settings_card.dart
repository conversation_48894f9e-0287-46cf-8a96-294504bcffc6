import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/currency_constants.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/settings_state.dart';
import 'setup_section_header.dart';
import 'radio_option.dart';

/// A widget for region settings in the setup screen
class RegionSettingsCard extends ConsumerWidget {
  /// Constructor
  const RegionSettingsCard({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(settingsProvider);

    return settingsAsync.when(
      data: (settings) => Card(
        margin: const EdgeInsets.all(8.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SetupSectionHeader(
                title: 'Region Settings',
                description: 'Language / Currency',
                icon: Icons.language,
              ),

              // Language Subsection
              const Text(
                'Language',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              const Text(
                'Select your preferred language for the app interface.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),

              _buildLanguageOptions(settings, ref),

              const SizedBox(height: 24),

              // Currency Subsection
              const Text(
                'Currency',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              const Text(
                'Select the currency for your meter readings.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),

              _buildCurrencyOptions(settings, ref),

              const SizedBox(height: 8),

              Text(
                'Tip: Select the currency that matches your electricity bills.',
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            ],
          ),
        ),
      ),
      loading: () => const Card(
        margin: EdgeInsets.all(8.0),
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, _) => Card(
        margin: const EdgeInsets.all(8.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(child: Text('Error loading settings: $error')),
        ),
      ),
    );
  }

  Widget _buildLanguageOptions(SettingsState settings, WidgetRef ref) {
    // Use Tier 1 languages (8) for setup screen to keep it manageable
    final languages = RegionalConstants.languages.take(8).toList();

    return Column(
      children: languages.map((lang) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: RadioOption<String>(
            value: lang.name,
            groupValue: settings.language,
            onChanged: (value) =>
                ref.read(settingsProvider.notifier).updateLanguage(value),
            title: '${lang.flagEmoji} ${lang.name}',
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCurrencyOptions(SettingsState settings, WidgetRef ref) {
    // Use first 12 currencies for setup screen to keep it manageable
    final currencies = RegionalConstants.currencies.take(12).toList();

    // Split currencies into two columns
    final int midPoint = (currencies.length / 2).ceil();
    final firstColumn = currencies.sublist(0, midPoint);
    final secondColumn = currencies.length > midPoint
        ? currencies.sublist(midPoint)
        : <CurrencyData>[];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First column
        Expanded(
          child: Column(
            children: firstColumn.map((currencyData) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: RadioOption<String>(
                  value: currencyData.code,
                  groupValue: settings.currency,
                  onChanged: (value) => ref
                      .read(settingsProvider.notifier)
                      .updateCurrency(value, currencyData.symbol),
                  title: '${currencyData.symbol} ${currencyData.code}',
                ),
              );
            }).toList(),
          ),
        ),

        // Second column
        Expanded(
          child: Column(
            children: secondColumn.map((currencyData) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: RadioOption<String>(
                  value: currencyData.code,
                  groupValue: settings.currency,
                  onChanged: (value) => ref
                      .read(settingsProvider.notifier)
                      .updateCurrency(value, currencyData.symbol),
                  title: '${currencyData.symbol} ${currencyData.code}',
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
