import 'package:flutter/material.dart';
import '../../../../core/constants/currency_constants.dart';
import 'setup_section_header.dart';
import 'radio_option.dart';

/// A widget for region settings in the setup screen
class RegionSettingsCard extends StatelessWidget {
  /// Current language
  final String language;

  /// Current currency
  final String currency;

  /// Currency symbol
  final String currencySymbol;

  /// Callback when language changes
  final Function(String) onLanguageChanged;

  /// Callback when currency changes
  final Function(String, String) onCurrencyChanged;

  /// Constructor
  const RegionSettingsCard({
    Key? key,
    required this.language,
    required this.currency,
    required this.currencySymbol,
    required this.onLanguageChanged,
    required this.onCurrencyChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SetupSectionHeader(
              title: 'Region Settings',
              description: 'Language / Currency',
              icon: Icons.language,
            ),

            // Language Subsection
            const Text(
              'Language',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Select your preferred language for the app interface.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            _buildLanguageOptions(),

            const SizedBox(height: 24),

            // Currency Subsection
            const Text(
              'Currency',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Select the currency for your meter readings.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            _buildCurrencyOptions(),

            const SizedBox(height: 8),

            Text(
              'Tip: Select the currency that matches your electricity bills.',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOptions() {
    // Use Tier 1 languages (8) for setup screen to keep it manageable
    final languages = RegionalConstants.languages.take(8).toList();

    return Column(
      children: languages.map((lang) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: RadioOption<String>(
            value: lang.name,
            groupValue: language,
            onChanged: onLanguageChanged,
            title: '${lang.flagEmoji} ${lang.name}',
            icon: Icons.language,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCurrencyOptions() {
    // Use first 12 currencies for setup screen to keep it manageable
    final currencies = RegionalConstants.currencies.take(12).toList();

    // Split currencies into two columns
    final int midPoint = (currencies.length / 2).ceil();
    final firstColumn = currencies.sublist(0, midPoint);
    final secondColumn = currencies.length > midPoint
        ? currencies.sublist(midPoint)
        : <CurrencyData>[];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First column
        Expanded(
          child: Column(
            children: firstColumn.map((currencyData) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: RadioOption<String>(
                  value: currencyData.code,
                  groupValue: currency,
                  onChanged: (value) =>
                      onCurrencyChanged(value, currencyData.symbol),
                  title: currencyData.code,
                  icon: currencyData.icon,
                ),
              );
            }).toList(),
          ),
        ),

        // Second column
        Expanded(
          child: Column(
            children: secondColumn.map((currencyData) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: RadioOption<String>(
                  value: currencyData.code,
                  groupValue: currency,
                  onChanged: (value) =>
                      onCurrencyChanged(value, currencyData.symbol),
                  title: currencyData.code,
                  icon: currencyData.icon,
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
