// File: lib/features/cost/presentation/widgets/cost_trend_chart.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/models/preference_state.dart';
import '../../../../core/providers/date_formatter_provider.dart';
import '../../../../core/localization/app_localizations.dart';
import '../models/chart_data.dart';

/// A chart that displays cost trend data over time
class CostTrendChart extends ConsumerStatefulWidget {
  /// The data to display in the chart
  final List<ChartData> data;

  /// The currency unit to display
  final String meterUnit;

  /// Whether to show the cost or usage values
  final bool showCost;

  /// User preferences for formatting
  final PreferenceState preferences;

  /// Constructor
  const CostTrendChart({
    super.key,
    required this.data,
    required this.meterUnit,
    required this.preferences,
    this.showCost = true,
  });

  @override
  ConsumerState<CostTrendChart> createState() => _CostTrendChartState();
}

class _CostTrendChartState extends ConsumerState<CostTrendChart> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return const Center(
        child: Text('No data'),
      );
    }

    return LineChart(
      LineChartData(
        lineTouchData: _getLineTouchData(),
        gridData: _getGridData(),
        titlesData: _getTitlesData(),
        borderData: _getBorderData(),
        lineBarsData: _getLineBarsData(),
        minX: 0,
        maxX: widget.data.length - 1.0,
        minY: 0,
        maxY: _getMaxY() * 1.2,
      ),
    );
  }

  LineTouchData _getLineTouchData() {
    return LineTouchData(
      touchTooltipData: LineTouchTooltipData(
        tooltipBgColor: AppColors.surfaceVariant,
        getTooltipItems: (List<LineBarSpot> touchedSpots) {
          return touchedSpots.map((spot) {
            final index = spot.x.toInt();
            if (index >= 0 && index < widget.data.length) {
              final chartData = widget.data[index];
              final value = widget.showCost ? chartData.cost : chartData.usage;

              // Format date according to user preferences
              final formattedDate = _formatDateForTooltip(chartData.date);

              // Format value with currency and /day suffix for cost
              final valueLabel = widget.showCost
                  ? _formatCurrencyPerDay(value)
                  : '${value.toStringAsFixed(2)} units';

              return LineTooltipItem(
                '$formattedDate\n',
                const TextStyle(
                  color: AppColors.onSurfaceVariant,
                  fontWeight: FontWeight.bold,
                ),
                children: [
                  TextSpan(
                    text: valueLabel,
                    style: TextStyle(
                      color: widget.showCost
                          ? AppColors.primary
                          : AppColors.secondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            }
            return null;
          }).toList();
        },
      ),
      touchCallback: (FlTouchEvent event, LineTouchResponse? touchResponse) {
        setState(() {
          if (event is FlPanEndEvent ||
              event is FlTapUpEvent ||
              touchResponse == null ||
              touchResponse.lineBarSpots == null ||
              touchResponse.lineBarSpots!.isEmpty) {
            touchedIndex = -1;
          } else {
            touchedIndex = touchResponse.lineBarSpots![0].x.toInt();
          }
        });
      },
      handleBuiltInTouches: true,
    );
  }

  FlGridData _getGridData() {
    return FlGridData(
      show: true,
      drawVerticalLine: false,
      horizontalInterval: _getMaxY() / 5,
      getDrawingHorizontalLine: (value) {
        return FlLine(
          color: AppColors.outline.withOpacity(0.2),
          strokeWidth: 1,
        );
      },
    );
  }

  FlTitlesData _getTitlesData() {
    final theme = Theme.of(context);

    return FlTitlesData(
      show: true,
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 30,
          interval: _getXAxisInterval(),
          getTitlesWidget: (value, meta) {
            if (value < 0 || value >= widget.data.length) {
              return const SizedBox();
            }
            final date = widget.data[value.toInt()].date;
            return Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                _getDateLabel(date),
                style: TextStyle(
                  color: theme.colorScheme.onSurface,
                  fontSize: 10,
                ),
              ),
            );
          },
        ),
      ),
      leftTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 40,
          getTitlesWidget: (value, meta) {
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Text(
                value.toStringAsFixed(0),
                style: TextStyle(
                  color: theme.colorScheme.onSurface,
                  fontSize: 10,
                ),
              ),
            );
          },
        ),
      ),
      topTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: false,
        ),
      ),
      rightTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: false,
        ),
      ),
    );
  }

  FlBorderData _getBorderData() {
    return FlBorderData(
      show: true,
      border: Border(
        bottom: BorderSide(
          color: AppColors.outline.withOpacity(0.5),
          width: 1,
        ),
        left: BorderSide(
          color: AppColors.outline.withOpacity(0.5),
          width: 1,
        ),
      ),
    );
  }

  List<LineChartBarData> _getLineBarsData() {
    return [
      LineChartBarData(
        spots: _getSpots(),
        isCurved: true,
        curveSmoothness: 0.3,
        color: widget.showCost ? AppColors.primary : AppColors.secondary,
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          getDotPainter: (spot, percent, barData, index) {
            return FlDotCirclePainter(
              radius: index == touchedIndex ? 5 : 3,
              color: widget.showCost ? AppColors.primary : AppColors.secondary,
              strokeWidth: 1,
              strokeColor: Colors.white,
            );
          },
        ),
        belowBarData: BarAreaData(
          show: true,
          color: widget.showCost
              ? const Color(0xFFFFA000).withOpacity(0.2)
              : AppColors.secondary.withOpacity(0.2),
        ),
      ),
    ];
  }

  List<FlSpot> _getSpots() {
    return List.generate(widget.data.length, (index) {
      final value =
          widget.showCost ? widget.data[index].cost : widget.data[index].usage;
      return FlSpot(index.toDouble(), value);
    });
  }

  double _getMaxY() {
    if (widget.data.isEmpty) {
      return 10;
    }

    if (widget.showCost) {
      final maxCost =
          widget.data.map((e) => e.cost).reduce((a, b) => a > b ? a : b);
      return maxCost > 0 ? maxCost : 10;
    } else {
      final maxUsage =
          widget.data.map((e) => e.usage).reduce((a, b) => a > b ? a : b);
      return maxUsage > 0 ? maxUsage : 10;
    }
  }

  /// Get x-axis interval for uncluttered display
  double _getXAxisInterval() {
    if (widget.data.length <= 5) {
      return 1.0; // Show all labels for 5 or fewer data points
    } else {
      // For more than 5 data points, show fewer labels
      return (widget.data.length / 4).ceilToDouble();
    }
  }

  String _getDateLabel(DateTime date) {
    final now = DateTime.now();

    // For many data points, show only months and years
    if (widget.data.length > 5) {
      if (date.year == now.year) {
        return DateTimeUtils.formatDate(date, 'MMM');
      } else {
        return DateTimeUtils.formatDate(date, 'MMM yy');
      }
    }

    // For fewer data points, show more detailed labels
    if (DateTimeUtils.isToday(date)) {
      return 'Today';
    } else if (DateTimeUtils.isYesterday(date)) {
      return 'Yesterday';
    } else if (date.year == now.year) {
      // Use user's date format preference for detailed labels
      return ref.watch(dateFormatterProvider).formatDateForChart(date);
    } else {
      return ref.watch(dateFormatterProvider).formatDateForChartWithYear(date);
    }
  }

  /// Format date for tooltip according to user preferences
  String _formatDateForTooltip(DateTime date) {
    return ref.watch(dateFormatterProvider).formatDateForHistory(date);
  }

  /// Format currency amount with /day suffix using user preferences
  String _formatCurrencyPerDay(double value) {
    final localizations = AppLocalizations.of(context);
    final formattedAmount = widget.preferences.formatCurrency(value);
    final perDayText = localizations.translate('perDay');
    return '$formattedAmount$perDayText';
  }
}
