import 'package:shared_preferences/shared_preferences.dart';
import '../di/service_locator.dart';
import '../utils/logger.dart';
import '../constants/preference_keys.dart';
import '../../features/home/<USER>/models/dashboard_state.dart';
import '../../features/validation/domain/services/data_integrity_service.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/data/notification_service.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../features/top_ups/domain/repositories/top_up_repository.dart';
import '../../features/averages/domain/services/average_service.dart';
import 'alert_prediction_service.dart';

/// Service for coordinating all alert types in foreground context
class AlertCoordinationService {
  static final AlertCoordinationService _instance =
      AlertCoordinationService._internal();

  factory AlertCoordinationService() => _instance;
  AlertCoordinationService._internal();

  /// Check all alert conditions and create notifications
  Future<void> checkAllAlerts() async {
    try {
      final settings = await _loadAlertSettings();

      if (!settings['notificationsEnabled']) {
        Logger.info('Notifications disabled, skipping alert checks');
        return;
      }

      final notifications = <AppNotification>[];

      // Check Low Balance condition with error handling
      if (settings['lowBalanceEnabled']) {
        try {
          final lowBalanceAlert = await _checkLowBalanceCondition();
          if (lowBalanceAlert != null) {
            notifications.add(lowBalanceAlert);
          }
        } catch (e) {
          Logger.error('Error checking low balance condition: $e');
          // Continue with other checks
        }
      }

      // Check Alert Threshold condition with error handling
      if (settings['timeToTopUpEnabled']) {
        try {
          final thresholdAlert = await _checkAlertThresholdCondition(
            settings['alertThreshold'],
            settings['daysInAdvance'],
          );
          if (thresholdAlert != null) {
            notifications.add(thresholdAlert);
          }
        } catch (e) {
          Logger.error('Error checking alert threshold condition: $e');
          // Continue with other checks
        }
      }

      // Check Meter Zero condition (24hrs before) with error handling
      if (settings['lowBalanceEnabled']) {
        try {
          final meterZeroAlert = await _checkMeterZeroCondition();
          if (meterZeroAlert != null) {
            notifications.add(meterZeroAlert);
          }
        } catch (e) {
          Logger.error('Error checking meter zero condition: $e');
          // Continue with other checks
        }
      }

      // Check Invalid Records condition with error handling
      if (settings['invalidRecordEnabled']) {
        try {
          final invalidAlerts = await _checkInvalidRecordCondition();
          notifications.addAll(invalidAlerts);
        } catch (e) {
          Logger.error('Error checking invalid record condition: $e');
          // Continue with notification creation
        }
      }

      // Create notifications if any conditions are met
      if (notifications.isNotEmpty) {
        try {
          final notificationService =
              await serviceLocator.getAsync<NotificationService>();

          // Batch multiple alerts into single notification for better UX
          if (notifications.length == 1) {
            await notificationService.showNotification(notifications.first);
          } else {
            await _createBatchedNotification(
                notifications, notificationService);
          }

          Logger.info('Created ${notifications.length} alert notifications');
        } catch (e) {
          Logger.error('Error getting notification service: $e');
        }
      } else {
        Logger.info('No alert conditions met');
      }

      // Update future alert predictions after checking current conditions
      await _updateAlertPredictions();
    } catch (e) {
      Logger.error('Critical error in alert checking: $e');
      // Attempt basic fallback notification if possible
      await _attemptFallbackNotification(e);
    }
  }

  /// Check alerts and update predictions (for data changes and app lifecycle)
  Future<void> checkAlertsAndUpdatePredictions() async {
    await checkAllAlerts();
  }

  /// Update alert predictions for future notifications
  Future<void> _updateAlertPredictions() async {
    try {
      final predictionService = AlertPredictionService();
      await predictionService.predictAndScheduleAlerts();
    } catch (e) {
      Logger.error('Error updating alert predictions: $e');
    }
  }

  /// Check low balance condition (< 24hrs to meter zero)
  Future<AppNotification?> _checkLowBalanceCondition() async {
    try {
      final dashboardState = await getDashboardState();
      if (dashboardState == null) return null;

      final daysToZero = dashboardState.calculateDaysToMeterZero();

      // Check if condition is met (< 24 hours)
      if (daysToZero == null || daysToZero >= 1.0) return null;

      // Check deduplication (once per day)
      if (await _wasNotificationSentToday(NotificationType.lowBalance)) {
        return null;
      }

      // Create notification
      await _setLastNotificationDate(NotificationType.lowBalance);

      final hoursRemaining = (daysToZero * 24).round();
      return AppNotification(
        title: 'Low Balance Alert',
        message: hoursRemaining > 0
            ? 'Your meter will reach zero in approximately $hoursRemaining hours.'
            : 'Your meter balance is critically low.',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );
    } catch (e) {
      Logger.error('Error checking low balance condition: $e');
      return null;
    }
  }

  /// Check alert threshold condition (< 24hrs to threshold)
  Future<AppNotification?> _checkAlertThresholdCondition(
    double alertThreshold,
    int daysInAdvance,
  ) async {
    try {
      final dashboardState = await getDashboardState();
      if (dashboardState == null) return null;

      final daysToThreshold = dashboardState.calculateDaysToAlertThreshold(
        alertThreshold,
        daysInAdvance,
      );

      // Check if condition is met (< 24 hours)
      if (daysToThreshold == null || daysToThreshold >= 1.0) return null;

      // Check deduplication (once per day)
      if (await _wasNotificationSentToday(NotificationType.timeToTopUp)) {
        return null;
      }

      // Create notification
      await _setLastNotificationDate(NotificationType.timeToTopUp);

      final hoursRemaining = (daysToThreshold * 24).round();
      return AppNotification(
        title: 'Time to Top-Up',
        message: hoursRemaining > 0
            ? 'You should top up in approximately $hoursRemaining hours.'
            : 'It\'s time to top up your meter.',
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      );
    } catch (e) {
      Logger.error('Error checking alert threshold condition: $e');
      return null;
    }
  }

  /// Check invalid record condition (any validation issues)
  Future<List<AppNotification>> _checkInvalidRecordCondition() async {
    try {
      final dataIntegrityService = serviceLocator<DataIntegrityService>();
      final issues = await dataIntegrityService.validateAllEntries();

      if (issues.isEmpty) return [];

      // Create notifications for validation issues (multiple allowed)
      final notifications = <AppNotification>[];

      // Group issues by severity for better messaging
      final highSeverityCount =
          issues.where((i) => i.severity.index == 2).length;
      final mediumSeverityCount =
          issues.where((i) => i.severity.index == 1).length;
      final lowSeverityCount =
          issues.where((i) => i.severity.index == 0).length;

      String message;
      if (highSeverityCount > 0) {
        message =
            'Found $highSeverityCount critical validation issue(s) that need immediate attention.';
      } else if (mediumSeverityCount > 0) {
        message =
            'Found $mediumSeverityCount validation issue(s) that should be reviewed.';
      } else {
        message = 'Found $lowSeverityCount minor validation issue(s) detected.';
      }

      notifications.add(AppNotification(
        title: 'Invalid Records Detected',
        message: message,
        timestamp: DateTime.now(),
        type: NotificationType.invalidRecord,
      ));

      return notifications;
    } catch (e) {
      Logger.error('Error checking invalid record condition: $e');
      return [];
    }
  }

  /// Check meter zero condition (24hrs before meter reaches zero)
  Future<AppNotification?> _checkMeterZeroCondition() async {
    try {
      final dashboardState = await getDashboardState();
      if (dashboardState == null) return null;

      final daysToZero = dashboardState.calculateDaysToMeterZero();

      // Check if condition is met (< 24 hours to zero)
      if (daysToZero == null || daysToZero >= 1.0 || daysToZero <= 0) {
        return null;
      }

      // Check deduplication (once per day)
      if (await _wasNotificationSentToday(NotificationType.lowBalance)) {
        return null;
      }

      return AppNotification(
        title: 'Critical: Meter Zero Alert',
        message:
            'Your meter will reach zero in less than 24 hours. Top up immediately to avoid power disconnection.',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );
    } catch (e) {
      Logger.error('Error checking meter zero condition: $e');
      return null;
    }
  }

  /// Get dashboard state for calculations
  Future<DashboardState?> getDashboardState() async {
    try {
      // Load data directly from repositories
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final topUpRepo = serviceLocator<TopUpRepository>();
      final averageService = serviceLocator<AverageService>();

      // Get latest meter reading
      final latestMeterReading = await meterReadingRepo.getLatestMeterReading();
      if (latestMeterReading == null) {
        Logger.info('No meter reading available for alert calculations');
        return null;
      }

      // Get averages
      final averageResult = await averageService.getAverages();

      // Calculate top-ups after latest reading
      final topUps = await topUpRepo.getTopUpsByDateRange(
        startDate: latestMeterReading.date,
        endDate: DateTime.now(),
      );

      final topUpsAfterLatest = topUps
          .where((topUp) => topUp.date.isAfter(latestMeterReading.date))
          .fold<double>(0.0, (sum, topUp) => sum + topUp.amount);

      // Create dashboard state for calculations
      return DashboardState(
        latestMeterReading: latestMeterReading,
        recentAverageDailyUsage: averageResult.recentAverage,
        totalAverageDailyUsage: averageResult.totalAverage,
        totalTopUpsAfterLatestReading: topUpsAfterLatest,
        recentEntries: const [], // Not needed for alert calculations
        isLoading: false,
      );
    } catch (e) {
      Logger.error('Error getting dashboard state: $e');
      return null;
    }
  }

  /// Load alert settings from preferences
  Future<Map<String, dynamic>> _loadAlertSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return {
        'notificationsEnabled':
            prefs.getBool(PreferenceKeys.notificationsEnabled) ?? false,
        'lowBalanceEnabled':
            prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false,
        'timeToTopUpEnabled':
            prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false,
        'invalidRecordEnabled':
            prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false,
        'alertThreshold': prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0,
        'daysInAdvance': prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5,
      };
    } catch (e) {
      Logger.error('Error loading alert settings: $e');
      return {
        'notificationsEnabled': false,
        'lowBalanceEnabled': false,
        'timeToTopUpEnabled': false,
        'invalidRecordEnabled': false,
        'alertThreshold': 5.0,
        'daysInAdvance': 5,
      };
    }
  }

  /// Check if notification was sent today for deduplication
  Future<bool> _wasNotificationSentToday(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      final lastDateString = prefs.getString(key);

      if (lastDateString == null) return false;

      final lastDate = DateTime.parse(lastDateString);
      final today = DateTime.now();

      return lastDate.year == today.year &&
          lastDate.month == today.month &&
          lastDate.day == today.day;
    } catch (e) {
      Logger.error('Error checking notification deduplication: $e');
      return false;
    }
  }

  /// Set last notification date for deduplication
  Future<void> _setLastNotificationDate(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      await prefs.setString(key, DateTime.now().toIso8601String());
    } catch (e) {
      Logger.error('Error setting notification date: $e');
    }
  }

  /// Get preference key for notification date tracking
  String _getNotificationDateKey(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return PreferenceKeys.lastLowBalanceNotificationDate;
      case NotificationType.timeToTopUp:
        return PreferenceKeys.lastTimeToTopUpNotificationDate;
      default:
        throw ArgumentError('Unsupported notification type: $type');
    }
  }

  /// Create a single batched notification from multiple alerts
  Future<void> _createBatchedNotification(
    List<AppNotification> notifications,
    NotificationService notificationService,
  ) async {
    // Sort notifications by priority (highest first)
    notifications.sort((a, b) => b.priorityLevel.compareTo(a.priorityLevel));

    // Create batched title and message
    final String title;
    final String message;

    if (notifications.length == 2) {
      title = 'Multiple Alerts';
      message = '${notifications[0].title} and ${notifications[1].title}';
    } else {
      title = 'Multiple Alerts (${notifications.length})';
      final firstTwo = notifications.take(2).map((n) => n.title).join(', ');
      final remaining = notifications.length - 2;
      message =
          '$firstTwo and $remaining more alert${remaining > 1 ? 's' : ''}';
    }

    // Use highest priority notification type for the batched notification
    final batchedNotification = AppNotification(
      title: title,
      message: message,
      timestamp: DateTime.now(),
      type: notifications.first.type,
    );

    await notificationService.showNotification(batchedNotification);

    // Set deduplication dates for all notification types
    for (final notification in notifications) {
      await _setLastNotificationDate(notification.type);
    }
  }

  /// Attempt to create a basic fallback notification when critical errors occur
  Future<void> _attemptFallbackNotification(dynamic error) async {
    try {
      // Only attempt fallback if we can access basic services
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();

      final fallbackNotification = AppNotification(
        title: 'Alert System Error',
        message:
            'There was an issue checking your meter alerts. Please check your data manually.',
        timestamp: DateTime.now(),
        type: NotificationType.invalidRecord,
      );

      await notificationService.showNotification(fallbackNotification);
      Logger.info('Fallback notification created due to critical error');
    } catch (fallbackError) {
      Logger.error('Failed to create fallback notification: $fallbackError');
      // If we can't even create a fallback notification, just log and continue
    }
  }
}
