import 'package:shared_preferences/shared_preferences.dart';
import '../di/service_locator.dart';
import '../utils/logger.dart';
import '../constants/preference_keys.dart';
import '../../features/home/<USER>/models/dashboard_state.dart';
import '../../features/validation/domain/services/data_integrity_service.dart';
import '../../features/notifications/domain/models/notification.dart';

/// Service for checking notification conditions and managing deduplication
class NotificationConditionService {
  static final NotificationConditionService _instance =
      NotificationConditionService._internal();

  factory NotificationConditionService() => _instance;
  NotificationConditionService._internal();

  /// Check all notification conditions and return notifications to create
  Future<List<AppNotification>> checkConditions({
    required bool lowBalanceEnabled,
    required bool timeToTopUpEnabled,
    required bool invalidRecordEnabled,
    required double alertThreshold,
    required int daysInAdvance,
  }) async {
    final notifications = <AppNotification>[];

    try {
      // Get dashboard state for calculations
      final dashboardState = await _getDashboardState();
      if (dashboardState == null) return notifications;

      // Check Low Balance condition (< 24hrs to meter zero)
      if (lowBalanceEnabled) {
        final lowBalanceNotification = await _checkLowBalanceCondition(
          dashboardState,
        );
        if (lowBalanceNotification != null) {
          notifications.add(lowBalanceNotification);
        }
      }

      // Check Time To Top Up condition (< 24hrs to threshold)
      if (timeToTopUpEnabled) {
        final topUpNotification = await _checkTimeToTopUpCondition(
          dashboardState,
          alertThreshold,
          daysInAdvance,
        );
        if (topUpNotification != null) {
          notifications.add(topUpNotification);
        }
      }

      // Check Invalid Record condition
      if (invalidRecordEnabled) {
        final invalidNotifications = await _checkInvalidRecordCondition();
        notifications.addAll(invalidNotifications);
      }

      return notifications;
    } catch (e) {
      Logger.error('Error checking notification conditions: $e');
      return notifications;
    }
  }

  /// Check low balance condition (< 24hrs to meter zero)
  Future<AppNotification?> _checkLowBalanceCondition(
    DashboardState dashboardState,
  ) async {
    try {
      final daysToZero = dashboardState.calculateDaysToMeterZero();

      // Check if condition is met (< 24 hours)
      if (daysToZero == null || daysToZero >= 1.0) return null;

      // Check deduplication (once per day)
      if (await _wasNotificationSentToday(NotificationType.lowBalance)) {
        return null;
      }

      // Create notification
      await _setLastNotificationDate(NotificationType.lowBalance);

      final hoursRemaining = (daysToZero * 24).round();
      return AppNotification(
        title: 'Low Balance Alert',
        message: hoursRemaining > 0
            ? 'Your meter will reach zero in approximately $hoursRemaining hours.'
            : 'Your meter balance is critically low.',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );
    } catch (e) {
      Logger.error('Error checking low balance condition: $e');
      return null;
    }
  }

  /// Check time to top up condition (< 24hrs to threshold)
  Future<AppNotification?> _checkTimeToTopUpCondition(
    DashboardState dashboardState,
    double alertThreshold,
    int daysInAdvance,
  ) async {
    try {
      final daysToThreshold = dashboardState.calculateDaysToAlertThreshold(
        alertThreshold,
        daysInAdvance,
      );

      // Check if condition is met (< 24 hours)
      if (daysToThreshold == null || daysToThreshold >= 1.0) return null;

      // Check deduplication (once per day)
      if (await _wasNotificationSentToday(NotificationType.timeToTopUp)) {
        return null;
      }

      // Create notification
      await _setLastNotificationDate(NotificationType.timeToTopUp);

      final hoursRemaining = (daysToThreshold * 24).round();
      return AppNotification(
        title: 'Time to Top-Up',
        message: hoursRemaining > 0
            ? 'You should top up in approximately $hoursRemaining hours.'
            : 'It\'s time to top up your meter.',
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      );
    } catch (e) {
      Logger.error('Error checking time to top up condition: $e');
      return null;
    }
  }

  /// Check invalid record condition (any validation issues)
  Future<List<AppNotification>> _checkInvalidRecordCondition() async {
    try {
      final dataIntegrityService = serviceLocator<DataIntegrityService>();
      final issues = await dataIntegrityService.validateAllEntries();

      if (issues.isEmpty) return [];

      // Create notifications for validation issues (multiple allowed)
      final notifications = <AppNotification>[];

      // Group issues by severity for better messaging
      final highSeverityCount =
          issues.where((i) => i.severity.index == 2).length;
      final mediumSeverityCount =
          issues.where((i) => i.severity.index == 1).length;
      final lowSeverityCount =
          issues.where((i) => i.severity.index == 0).length;

      String message;
      if (highSeverityCount > 0) {
        message =
            'Found $highSeverityCount critical validation issue(s) that need immediate attention.';
      } else if (mediumSeverityCount > 0) {
        message =
            'Found $mediumSeverityCount validation issue(s) that should be reviewed.';
      } else {
        message = 'Found $lowSeverityCount minor validation issue(s) detected.';
      }

      notifications.add(AppNotification(
        title: 'Invalid Records Detected',
        message: message,
        timestamp: DateTime.now(),
        type: NotificationType.invalidRecord,
      ));

      return notifications;
    } catch (e) {
      Logger.error('Error checking invalid record condition: $e');
      return [];
    }
  }

  /// Get dashboard state for calculations
  Future<DashboardState?> _getDashboardState() async {
    try {
      // This is a simplified approach for background execution
      // In a real background context, we'd need to recreate the provider
      // For now, we'll return null and handle this in the background worker
      return null;
    } catch (e) {
      Logger.error('Error getting dashboard state: $e');
      return null;
    }
  }

  /// Check if notification was sent today for deduplication
  Future<bool> _wasNotificationSentToday(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      final lastDateString = prefs.getString(key);

      if (lastDateString == null) return false;

      final lastDate = DateTime.parse(lastDateString);
      final today = DateTime.now();

      return lastDate.year == today.year &&
          lastDate.month == today.month &&
          lastDate.day == today.day;
    } catch (e) {
      Logger.error('Error checking notification deduplication: $e');
      return false;
    }
  }

  /// Set last notification date for deduplication
  Future<void> _setLastNotificationDate(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      await prefs.setString(key, DateTime.now().toIso8601String());
    } catch (e) {
      Logger.error('Error setting notification date: $e');
    }
  }

  /// Get preference key for notification date tracking
  String _getNotificationDateKey(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return PreferenceKeys.lastLowBalanceNotificationDate;
      case NotificationType.timeToTopUp:
        return PreferenceKeys.lastTimeToTopUpNotificationDate;
      default:
        throw ArgumentError('Unsupported notification type: $type');
    }
  }
}
